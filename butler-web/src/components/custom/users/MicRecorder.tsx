/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { Icon } from "@iconify/react/dist/iconify.js";
import { useEffect, useState, useRef, useCallback } from "react";
import { SupportedLanguage } from "../../../../hooks/useLanguagePreference";

interface MicRecorderProps {
  setUserMessage: (message: string) => void;
  language?: SupportedLanguage;
  disabled?: boolean;
  className?: string;
}

// Language mapping for speech recognition
const getRecognitionLanguage = (language: SupportedLanguage): string => {
  const languageMap: Record<SupportedLanguage, string> = {
    en: "en-US",
    hi: "hi-IN",
    "hi-en": "hi-IN", // Will fallback to Hindi, but can understand English too
    ta: "ta-IN",
    te: "te-IN",
    bn: "bn-IN",
    mr: "mr-IN",
    gu: "gu-IN",
  };
  return languageMap[language] || "en-US";
};

const MicRecorder = ({
  setUserMessage,
  language = "en",
  disabled = false,
  className = "",
}: MicRecorderProps) => {
  const [isListening, setIsListening] = useState(false);
  const [isSupported, setIsSupported] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [transcript, setTranscript] = useState("");
  const [showTooltip, setShowTooltip] = useState(false);
  const recognitionRef = useRef<any | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize speech recognition
  const initializeSpeechRecognition = useCallback(() => {
    if (typeof window === "undefined") return;

    const SpeechRecognition =
      (window as any).webkitSpeechRecognition ||
      (window as any).SpeechRecognition;

    if (!SpeechRecognition) {
      setIsSupported(false);
      return;
    }

    const recognition = new SpeechRecognition();
    recognition.lang = getRecognitionLanguage(language);
    recognition.interimResults = true;
    recognition.continuous = false;
    recognition.maxAlternatives = 1;

    // Mobile-specific optimizations
    if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
      recognition.interimResults = false; // Better for mobile performance
    }

    // Handle results
    recognition.onresult = (event: any) => {
      let finalTranscript = "";

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript;
        if (event.results[i].isFinal) {
          finalTranscript += transcript;
        }
      }

      if (finalTranscript) {
        setTranscript(finalTranscript.trim());
        setIsListening(false);
      }
    };

    // Handle errors
    recognition.onerror = (event: any) => {
      console.error("Speech recognition error:", event.error);
      setError(event.error);
      setIsListening(false);

      // Clear error after 3 seconds
      setTimeout(() => setError(null), 3000);
    };

    // Handle end
    recognition.onend = () => {
      setIsListening(false);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };

    // Handle start
    recognition.onstart = () => {
      setError(null);
      setTranscript("");

      // Auto-stop after 10 seconds to prevent hanging
      timeoutRef.current = setTimeout(() => {
        if (recognitionRef.current && isListening) {
          recognitionRef.current.stop();
        }
      }, 10000);
    };

    recognitionRef.current = recognition;
  }, [language, isListening]);

  // Initialize on mount and language change
  useEffect(() => {
    initializeSpeechRecognition();

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [initializeSpeechRecognition]);

  // Handle transcript changes
  useEffect(() => {
    if (transcript && transcript.trim()) {
      setUserMessage(transcript);
      setTranscript("");
    }
  }, [transcript, setUserMessage]);

  const toggleListening = useCallback(async () => {
    if (!recognitionRef.current || disabled) return;

    if (isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    } else {
      try {
        // Request microphone permission on mobile devices
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
          try {
            const stream = await navigator.mediaDevices.getUserMedia({
              audio: true,
            });
            // Stop the stream immediately as we only needed permission
            stream.getTracks().forEach((track) => track.stop());
          } catch (permissionError) {
            console.error("Microphone permission denied:", permissionError);
            setError("not-allowed");
            setTimeout(() => setError(null), 3000);
            return;
          }
        }

        recognitionRef.current.start();
        setIsListening(true);
      } catch (err) {
        console.error("Failed to start speech recognition:", err);
        setError("Failed to start recording");
        setTimeout(() => setError(null), 3000);
      }
    }
  }, [isListening, disabled]);

  // Don't render if not supported
  if (!isSupported) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={toggleListening}
        disabled={disabled}
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
        className={`h-12 w-12 md:h-14 md:w-14 rounded-2xl shadow-lg transition-all duration-200 flex items-center justify-center touch-manipulation ${
          disabled
            ? "opacity-50 cursor-not-allowed bg-gray-100"
            : isListening
            ? "bg-red-50 border-2 border-red-200 hover:bg-red-100 active:bg-red-200"
            : "bg-gray-50 border-2 border-gray-200 hover:bg-gray-100 active:bg-gray-200"
        }`}
        type="button"
        aria-label={isListening ? "Stop recording" : "Start voice recording"}
        style={{
          WebkitTapHighlightColor: "transparent", // Remove tap highlight on iOS
          userSelect: "none", // Prevent text selection
        }}
      >
        {isListening ? (
          <div className="flex items-center justify-center">
            <div className="w-6 h-6 rounded-full bg-red-500 animate-pulse relative">
              <div className="absolute inset-0 rounded-full bg-red-500 animate-ping opacity-75" />
            </div>
          </div>
        ) : (
          <Icon
            icon="fluent:mic-20-regular"
            width="24"
            height="24"
            className={error ? "text-red-500" : "text-gray-600"}
          />
        )}
      </button>

      {/* Error tooltip */}
      {error && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-red-500 text-white text-xs rounded-lg whitespace-nowrap z-50 shadow-lg">
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-red-500" />
          {error === "not-allowed"
            ? "Microphone access denied"
            : error === "no-speech"
            ? "No speech detected"
            : error === "network"
            ? "Network error"
            : error === "audio-capture"
            ? "Microphone not available"
            : error === "aborted"
            ? "Recording cancelled"
            : "Recording failed"}
        </div>
      )}

      {/* Listening indicator */}
      {isListening && (
        <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
        </div>
      )}

      {/* Help tooltip */}
      {showTooltip && !isListening && !error && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg whitespace-nowrap z-50 shadow-lg">
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800" />
          Click to speak your message
        </div>
      )}
    </div>
  );
};

export default MicRecorder;
